import lang from "./lang";

const {
  manageIdea,
} = lang;

export const mobileWidthLimit = 480;
export const tabletWidthLimit = 768;
export const lowResDeskLimit = 1024;
export const highResDeskLimit = 1280;

export const FILE_SIZE_10_MB = 10000000;
export const acceptedImageMimeTypes = [
  "image/jpeg",
  "image/png",
  "image/svg+xml",
  "image/webp",
  "image/gif",
  "image/apng",
  "image/avif",
];
export const agentLoadingStates = manageIdea.promptLoadingStates;

export const emailRegex =
  /^(?!.*\.{2})([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;
export const usernameRegex = /^[A-Za-z0-9_]{4,15}$/;

export const SupabaseTables = {
  Projects: process.env.NEXT_PUBLIC_TABLE_MEDIA_PROJECTS || '',
};

// Platform character limits
export const PLATFORM_CHARACTER_LIMITS = {
  TWITTER: 280,
  TWITTER_PREMIUM: 25000,
  LINKEDIN: 3000,
  INSTAGRAM: 2200,
  FACEBOOK: 63206,
  YOUTUBE: 5000,
} as const;

export const PLATFORM_CANVAS_SIZES = {
  twitter: { 
    width: 1200, 
    height: 1200,
  },
  x: { 
    width: 1200, 
    height: 1200,
  },
  instagram: { 
    width: 1080, 
    height: 1080,
  },
  linkedin: { 
    width: 1200, 
    height: 628,
  },
  facebook: { 
    width: 1200, 
    height: 628,
  },
  youtube: { 
    width: 1280, 
    height: 720,
  },
  default: { 
    width: 1200, 
    height: 1200,
  },
} as const;

export const tones = [
  "Professional",
  "Gen-Z",
  "Casual",
  "Academic",
  "Mentor",
  "Creative",
];

export const mixpanelToken = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;
export const isProd = process.env.NEXT_PUBLIC_ROOT_DOMAIN === "mediapilot.app";

export const ImageStyles = [
  {
    option: "none",
    label: "No Style",
  },
  {
    option: "photorealistic",
    label: "Photorealistic",
  },
  {
    option: "watercolor",
    label: "Watercolor",
  },
  {
    option: "pixel_art",
    label: "Pixel Art",
  },
  {
    option: "oil_painting",
    label: "Oil Painting",
  },
  {
    option: "pencil_sketch",
    label: "Pencil Sketch",
  },
  {
    option: "cyberpunk",
    label: "Cyberpunk",
  },
  {
    option: "impressionist",
    label: "Impressionist",
  },
  {
    option: "abstract",
    label: "Abstract",
  },
  {
    option: "pop_art",
    label: "Pop Art",
  },
  {
    option: "isometric",
    label: "Isometric",
  },
  {
    option: "ukiyo_e",
    label: "Ukiyo-e",
  },
  {
    option: "low_poly",
    label: "Low Poly",
  },
];

export const tonesSelection = [
  {
    title: "👨 Professional",
    value: "Professional",
  },
  {
    title: "🧑‍💻 Gen-Z",
    value: "Gen-Z",
  },
  {
    title: "🤙 Casual",
    value: "Casual",
  },
  {
    title: "👨‍🏫 Academic",
    value: "Academic",
  },
  {
    title: "🧑‍🏫 Mentor",
    value: "Mentor",
  },
  {
    title: "👨‍🎨 Creative",
    value: "Creative",
  },
];

export const basicPlanLink = 'https://buy.stripe.com/test_fZeg342659J6cqQ3cc'

export const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  'text/markdown',
  'text/plain',
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024;
