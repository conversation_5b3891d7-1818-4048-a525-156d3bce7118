'use client'

import {
  useState,
  useEffect,
  useMemo,
  useCallback,
} from 'react';
import {
  useRouter,
  useSearchParams,
} from 'next/navigation';
import {
  FacebookIcon, InstagramIcon, LinkedInColorIcon, XIcon, YoutubeIcon,
} from '@/common/components/icons';
import { useProjects } from '@/common/hooks';
import { useTwitterPremium } from '@/common/hooks/useTwitterPremium';
import toast from 'react-hot-toast';
import { 
  schedulePostUrl, updatePostUrl, fetchTweetsUrl,
} from '@/common/utils/network/endpoints';
import {
  TimeSelector,
  DateSelector,
  PlatformContentTabs,
  SchedulePostActions,
  SocialContent,
  getNextAvailableTime,
} from '@/common/components/molecules/calendar/schedulePost';
import { PLATFORM_CHARACTER_LIMITS } from '@/common/constants';
import { DashboardLayout } from '@/common/components/organisms';
import { 
  Button,
} from '@/common/components/atoms';
import { routes } from '@/common/routes';
import ImageModal from '@/common/components/molecules/calendar/schedulePost/ImageModal';
import { ScheduledPost } from '@/common/hooks/usePosts';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';

export default function SchedulePostPage () {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { activeProject } = useProjects();
  const { isPremium } = useTwitterPremium();
  const { trackPostEvent } = useMixpanelEvent();

  const initialDateParam = searchParams.get('date');
  const initialDate = useMemo(() =>
    initialDateParam ? new Date(initialDateParam) : new Date(),
  [initialDateParam],
  );

  const planId = searchParams.get('planId') || '';
  const postId = searchParams.get('postId');
  const isEditMode = !!postId;

  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [activeMediaPlatformId, setActiveMediaPlatformId] = useState<string | null>(null);
  const [platforms, setPlatforms] = useState<SocialContent[]>([]);
  const [isScheduling, setIsScheduling] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(initialDate);
  const [editingPost, setEditingPost] = useState<ScheduledPost | null>(null);
  const [isLoadingPost, setIsLoadingPost] = useState(false);

  const initialTime = useMemo(() => {
    const isToday = initialDate.toDateString() === new Date().toDateString();

    if (isToday) {
      return getNextAvailableTime();
    } else {
      return {
        hour: 9,
        minute: 0,
        isAM: true,
      };
    }
  }, [initialDate]);

  const [selectedHour, setSelectedHour] = useState(initialTime.hour);
  const [selectedMinute, setSelectedMinute] = useState(initialTime.minute);
  const [isAM, setIsAM] = useState(initialTime.isAM);
  const [activeTab, setActiveTab] = useState<string | null>(null);

  const linkedInAccount = activeProject?.accounts?.find(acc => acc.platform === 'LinkedIn' && acc.connected);
  const twitterAccount = activeProject?.accounts?.find(acc => acc.platform === 'Twitter' && acc.connected);
  const instagramAccount = activeProject?.accounts?.find(acc => acc.platform === 'Instagram' && acc.connected);

  const getCharacterLimit = useCallback((platformId: string): number | undefined => {
    const platformName = platformId.toUpperCase();
    if (platformName === 'TWITTER' || platformName === 'X') {
      if (isPremium) {
        return PLATFORM_CHARACTER_LIMITS.TWITTER_PREMIUM;
      }

      return PLATFORM_CHARACTER_LIMITS.TWITTER;
    }
    if (platformName === 'LINKEDIN') {
      return PLATFORM_CHARACTER_LIMITS.LINKEDIN;
    }
    if (platformName === 'INSTAGRAM') {
      return PLATFORM_CHARACTER_LIMITS.INSTAGRAM;
    }
    if (platformName === 'FACEBOOK') {
      return PLATFORM_CHARACTER_LIMITS.FACEBOOK;
    }
    if (platformName === 'YOUTUBE') {
      return PLATFORM_CHARACTER_LIMITS.YOUTUBE;
    }
    return undefined;
  }, [isPremium]);

  const hasCharacterLimitViolation = useMemo(() => {
    const selectedPlatforms = platforms.filter(p => p.selected && p.connected);
    return selectedPlatforms.some(platform => {
      const limit = getCharacterLimit(platform.id);
      if (!limit) {
        return false;
      }
      const textContent = platform.content.replace(/<[^>]*>/g, '');
      return textContent.length > limit;
    });
  }, [platforms, getCharacterLimit]);

  const currentAgentId = useMemo(() => {
    if (isEditMode && editingPost) {
      const account = activeProject?.accounts?.find(acc =>
        acc.platform.toLowerCase() === editingPost.platform.toLowerCase() && acc.connected,
      );
      return account?.agentId || '';
    }
    return linkedInAccount?.agentId || twitterAccount?.agentId || instagramAccount?.agentId || '';
  }, [isEditMode, editingPost, activeProject?.accounts, linkedInAccount?.agentId, twitterAccount?.agentId, instagramAccount?.agentId]);

  const loadPostData = useCallback(async () => {
    if (!postId) {
      console.warn('No postId provided for edit mode');
      return;
    }

    if (!activeProject?.accounts) {
      return
    }

    if (activeProject?.accounts && activeProject.accounts.length === 0) {
      toast.error('Unable to load post data: no connected accounts found');
      return;
    }

    setIsLoadingPost(true);
    try {
      const connectedAccounts = activeProject.accounts.filter(acc => acc.connected && acc.agentId);

      if (connectedAccounts.length === 0) {
        throw new Error('No connected accounts with agent IDs found');
      }

      let foundPost: ScheduledPost | null = null;
      let foundPlanId: string | null = null;
      let foundAgentId: string | null = null;
      let foundPlatform: string | null = null;

      for (const account of connectedAccounts) {
        try {
          if (!account.agentId) {
            continue;
          }

          const url = fetchTweetsUrl.replace('%agentId%', account.agentId);
          const response = await fetch(url);

          if (!response.ok) {
            continue;
          }

          const data = await response.json();

          if (!data.plan || !data.plan.posts) {
            continue;
          }

          const post = data.plan.posts.find((p: ScheduledPost) => p.id === postId);

          if (post) {
            foundPost = post;
            foundPlanId = data.plan.id;
            foundAgentId = account.agentId;
            foundPlatform = account.platform;
            break;
          }
        } catch (accountError) {
          continue;
        }
      }

      if (foundPost && foundAgentId && foundPlatform) {
        const completePost: ScheduledPost = {
          ...foundPost,
          platform: foundPlatform,
          planId: foundPlanId || planId || '',
          attachments: foundPost.attachments || [],
        };

        setEditingPost(completePost);

        const postDate = new Date(foundPost.scheduledTime);
        if (isNaN(postDate.getTime())) {
          throw new Error('Invalid scheduled time in post data');
        }

        setSelectedDate(new Date(postDate.getFullYear(), postDate.getMonth(), postDate.getDate()));

        let hours = postDate.getHours();
        const minutes = postDate.getMinutes();

        const isPM = hours >= 12;
        if (isPM) {
          hours = hours === 12 ? 12 : hours - 12;
          setIsAM(false);
        } else {
          hours = hours === 0 ? 12 : hours;
          setIsAM(true);
        }

        setSelectedHour(hours);
        setSelectedMinute(minutes);
      } else {
        const checkedPlatforms = connectedAccounts.map(acc => acc.platform).join(', ');
        throw new Error(`Post with ID ${postId} not found in any connected account. Checked platforms: ${checkedPlatforms}`);
      }
    } catch (error) {
      console.error('Error loading post data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error(`Failed to load post data: ${errorMessage}`);
    } finally {
      setIsLoadingPost(false);
    }
  }, [postId, activeProject?.accounts, planId]);

  const platformIcons = useMemo(() => ({
    facebook: <FacebookIcon width={14} height={14} />,
    instagram: <InstagramIcon width={14} height={14} />,
    linkedin: <LinkedInColorIcon width={14} height={14} />,
    x: <XIcon width={14} height={14} />,
    youtube: <YoutubeIcon width={14} height={14} />,
  }), []);

  const platformNameMapping = useMemo(() => ({
    'Facebook': 'facebook',
    'Instagram': 'instagram',
    'LinkedIn': 'linkedin',
    'Twitter': 'x',
    'YouTube': 'youtube',
  }), []);

  useEffect(() => {
    if (isEditMode && postId) {
      loadPostData();
    }
  }, [isEditMode, loadPostData, postId]);

  useEffect(() => {
    if (!activeProject?.accounts) {
      return;
    }

    if (isEditMode && isLoadingPost) {
      return;
    }

    if (isEditMode && editingPost) {
      const platformId = editingPost.platform.toLowerCase();
      const iconKey = platformNameMapping[editingPost.platform as keyof typeof platformNameMapping];
      const icon = iconKey ? platformIcons[iconKey as keyof typeof platformIcons] : null;

      setPlatforms([{
        id: platformId,
        name: editingPost.platform,
        icon: icon || <div></div>,
        connected: true,
        selected: true,
        content: editingPost.content || '',
        attachments: editingPost.attachments || [],
      }]);
      setActiveTab(platformId);
      return;
    }

    const platformsData: SocialContent[] = activeProject.accounts.map((account) => {
      const iconKey = platformNameMapping[account.platform as keyof typeof platformNameMapping];
      const icon = iconKey ? platformIcons[iconKey as keyof typeof platformIcons] : null;

      return {
        id: account.platform,
        name: account.platform.charAt(0).toUpperCase() + account.platform.slice(1),
        icon: icon || <div></div>,
        connected: account.connected,
        selected: account.connected,
        content: '',
      };
    });

    setPlatforms(platformsData);

    const firstConnected = platformsData.find(p => p.connected);
    if (firstConnected) {
      setActiveTab(firstConnected.id);
    }
  }, [activeProject, platformIcons, platformNameMapping, isEditMode, editingPost, isLoadingPost]);

  const handleContentChange = (platformId: string, content: string) => {
    setPlatforms(prev => prev.map(platform => {
      if (platform.id === platformId) {
        return {
          ...platform,
          content,
        };
      }
      return platform;
    }));
  };

  const handleHourChange = (hour: number) => {
    setSelectedHour(hour);
  };

  const handleMinuteChange = (minute: number) => {
    setSelectedMinute(minute);
  };

  const handleAMPMToggle = () => {
    setIsAM(!isAM);
  };

  const handleUpdatePost = async () => {
    if (!editingPost) {
      toast.error('Missing post information');
      return;
    }

    const postAccount = activeProject?.accounts?.find(acc =>
      acc.platform.toLowerCase() === editingPost.platform.toLowerCase() && acc.connected,
    );

    if (!postAccount?.agentId) {
      toast.error(`No connected agent found for ${editingPost.platform}`);
      return;
    }

    const platform = platforms[0];
    if (!platform || !platform.content.trim()) {
      toast.error('Please enter content for your post');
      return;
    }

    if (hasCharacterLimitViolation) {
      const limit = getCharacterLimit(platform.id);
      const textContent = platform.content.replace(/<[^>]*>/g, '');
      toast.error(`Content exceeds character limit. Current: ${textContent.length}, Limit: ${limit}`);
      return;
    }

    setIsScheduling(true);

    try {
      const scheduledDate = new Date(selectedDate);
      const hour = isAM ? selectedHour : (selectedHour === 12 ? 12 : selectedHour + 12);
      scheduledDate.setHours(hour, selectedMinute, 0, 0);

      const url = updatePostUrl.replace('%agentId%', postAccount.agentId);
      const useFormData = platform.file instanceof File;

      let response;

      if (useFormData) {
        const formData = new FormData();
        formData.append('content', platform.content);
        formData.append('postId', editingPost.id);
        formData.append('planId', editingPost.planId);
        formData.append('scheduledTime', scheduledDate.toISOString());

        if (platform.file) {
          formData.append('image', platform.file);
        }

        if (platform.attachments?.[0]?.includes('/generated/')) {
          formData.append('imageFromAI', platform.attachments[0]);
        }

        response = await fetch(url, {
          method: 'POST',
          body: formData,
        });
      } else {
        const requestBody = {
          content: platform.content,
          postId: editingPost.id,
          planId: editingPost.planId,
          scheduledTime: scheduledDate.toISOString(),
          imageFromAI: platform.attachments?.[0]?.includes('/generated/') ? platform.attachments[0] : undefined,
        };

        response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update post');
      }

      const formattedTime = scheduledDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });

      const formattedDate = scheduledDate.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric',
      });

      toast.success(`Post updated for ${editingPost.platform} on ${formattedDate} at ${formattedTime}`);

      trackPostEvent('updated', {
        platforms: [editingPost.platform],
        hasImage: platform.attachments && platform.attachments.length > 0,
        scheduledTime: scheduledDate.toISOString(),
        contentLength: platform.content.replace(/<[^>]*>/g, '').length,
      });

      router.push(routes.dashboardCalendarPath);
    } catch (error) {
      console.error('Error updating post:', error);
      if (error instanceof Error) {
        toast.error(`Failed to update post: ${error.message}`);
      } else {
        toast.error('Failed to update post');
      }
    } finally {
      setIsScheduling(false);
    }
  };

  const handleSchedulePost = async () => {
    if (isEditMode) {
      return handleUpdatePost();
    }

    const selectedPlatforms = platforms.filter(p => p.selected && p.connected && p.content.trim());

    if (selectedPlatforms.length === 0) {
      toast.error('Please select at least one platform');
      return;
    }

    const hasContent = selectedPlatforms.some(p => p.content.trim());

    if (!hasContent) {
      toast.error('Please enter content for at least one selected platform');
      return;
    }

    const scheduledDate = new Date(selectedDate);
    const hour = isAM ? selectedHour : (selectedHour === 12 ? 12 : selectedHour + 12);
    scheduledDate.setHours(hour, selectedMinute, 0, 0);

    const now = new Date();
    if (scheduledDate < now) {
      toast.error('Cannot schedule posts in the past. Please select a valid time.');
      return;
    }

    setIsScheduling(true);

    try {
      const platformsByAgent: Record<string, string[]> = {};

      selectedPlatforms.forEach(platform => {
        const account = activeProject?.accounts?.find(acc => acc.platform === platform.id);
        if (account?.agentId) {
          if (!platformsByAgent[account.agentId]) {
            platformsByAgent[account.agentId] = [];
          }
          platformsByAgent[account.agentId].push(platform.id);
        }
      });

      const apiCalls = Object.entries(platformsByAgent).map(async ([agentId, platformIds]) => {
        const url = schedulePostUrl.replace('%agentId%', agentId);

        const platform = selectedPlatforms.find(p => platformIds.includes(p.id));
        const content = platform?.content || '';

        const platformWithFile = selectedPlatforms.find(
          p => platformIds.includes(p.id) && p.file instanceof File,
        );

        const imageFromAI = platform?.attachments?.[0]?.includes('/generated/')
          ? platform.attachments[0]
          : undefined;

        const useFormData = platformWithFile !== undefined;

        let response;

        if (useFormData) {
          const formData = new FormData();
          formData.append('content', content);
          formData.append('scheduledTime', scheduledDate.toISOString());
          formData.append('platform', platformIds[0].toLowerCase());

          if (planId) {
            formData.append('planId', planId);
          }

          if (platformWithFile?.file) {
            formData.append('image', platformWithFile.file);
          }

          if (imageFromAI) {
            formData.append('imageFromAI', imageFromAI);
          }

          response = await fetch(url, {
            method: 'POST',
            body: formData,
          });
        } else {
          const requestBody: {
            content: string;
            scheduledTime: string;
            platform: string;
            imageFromAI?: string;
            planId?: string;
          } = {
            content,
            scheduledTime: scheduledDate.toISOString(),
            platform: platformIds[0].toLowerCase(),
            imageFromAI,
          };

          // Only include planId if it exists (for new posts, planId might be empty)
          if (planId) {
            requestBody.planId = planId;
          }

          response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
          });
        }

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `Failed to schedule post for ${platformIds.join(', ')}`);
        }

        return {
          success: true,
          platforms: platformIds,
        };
      });

      const results = await Promise.allSettled(apiCalls);
      let totalSuccessCount = 0;

      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          totalSuccessCount += result.value.platforms.length;
        }
      });

      const formattedDate = scheduledDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });

      const formattedTime = scheduledDate.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });

      const platformNames = selectedPlatforms.map(p => p.name).join(', ');

      if (totalSuccessCount === selectedPlatforms.length) {
        toast.success(`Post scheduled for ${platformNames} on ${formattedDate} at ${formattedTime}`);
      } else {
        toast.success(`Post scheduled for ${totalSuccessCount} of ${selectedPlatforms.length} platforms on ${formattedDate} at ${formattedTime}`);
      }

      trackPostEvent('scheduled', {
        platforms: selectedPlatforms.map(p => p.id),
        hasImage: selectedPlatforms.some(p => p.attachments && p.attachments.length > 0),
        scheduledTime: scheduledDate.toISOString(),
        contentLength: selectedPlatforms.reduce((total, p) => total + p.content.replace(/<[^>]*>/g, '').length, 0),
      });

      router.push(routes.dashboardCalendarPath);
    } catch (error) {
      console.error('Error scheduling post:', error);
      if (error instanceof Error) {
        toast.error(`Failed to schedule post: ${error.message}`);
      } else {
        toast.error('Failed to schedule post');
      }
    } finally {
      setIsScheduling(false);
    }
  };

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
  };

  const handleCancel = () => {
    router.push(routes.dashboardCalendarPath);
  };

  const handleImageAttached = async (imageUrl: string, isFromAI: boolean, file?: File) => {
    if (activeMediaPlatformId) {
      setPlatforms(prevPlatforms =>
        prevPlatforms.map(platform => {
          if (platform.id === activeMediaPlatformId) {
            return {
              ...platform,
              file: !isFromAI && file ? file : platform.file,
              attachments: [imageUrl],
            };
          }
          return platform;
        }),
      );
    }
  };
  const handleRemoveImage = () => {
    setPlatforms(prevPlatforms =>
      prevPlatforms.map(platform => {
        if (platform.id === activeMediaPlatformId) {
          return {
            ...platform,
            file: undefined,
            attachments: [],
          };
        }
        return platform;
      }),
    );
  };
  if (isEditMode && !planId) {
    return (
      <DashboardLayout>
        <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-white mb-4">Missing Plan Information</h2>
            <p className="text-gray-400 mb-6">Unable to edit post without plan information.</p>
            <Button
              variant="outline"
              size="md"
              onClick={handleCancel}
            >
              Back to Calendar
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <>
      <ImageModal
        isOpen={isImageModalOpen}
        onClose={() => setIsImageModalOpen(false)}
        agentId={(() => {
          if (activeMediaPlatformId) {
            const account = activeProject?.accounts?.find(acc =>
              acc.platform.toLowerCase() === activeMediaPlatformId.toLowerCase() && acc.connected,
            );
            return account?.agentId || currentAgentId;
          }
          return currentAgentId;
        })()}
        planId={planId}
        content={platforms.find(p => p.id === activeMediaPlatformId)?.content || ''}
        onImageAttached={handleImageAttached}
        initialImage={platforms.find(p => p.id === activeMediaPlatformId)?.attachments?.[0]}
        platform={activeMediaPlatformId || undefined}
      />
      <DashboardLayout>
        <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6">
          <div className="flex items-center justify-between mb-2">
            <div>
              <h2 className="text-xl font-semibold text-white">
                {isEditMode ? `Edit Post for ${editingPost?.platform}` : 'Schedule Post'}
              </h2>
              <p className="text-gray-400 text-sm mt-1">
                {isEditMode ? 'Update your scheduled social media content' : 'Create and schedule your social media content'}
              </p>
            </div>
          </div>

          <div className='flex items-center gap-2 mb-8'>
            <DateSelector
              selectedDate={selectedDate}
              onDateChange={handleDateChange}
            />
            <TimeSelector
              selectedHour={selectedHour}
              selectedMinute={selectedMinute}
              isAM={isAM}
              selectedDate={selectedDate}
              onHourChange={handleHourChange}
              onMinuteChange={handleMinuteChange}
              onAMPMToggle={handleAMPMToggle}
            />
          </div>

          {platforms.length > 0 && !isEditMode && (
            <div className="mb-8">
              <h3 className="text-white font-medium mb-3">Prepare your Content</h3>
              <div className="flex flex-wrap gap-2">
                {platforms
                  .filter((platform) => platform.connected)
                  .map((platform) => (
                    <button
                      key={platform.id}
                      onClick={() => setActiveTab(platform.id)}
                      className={`gap-2 flex items-center py-1.5 px-3 text-sm font-medium focus:outline-none rounded-lg border transition-all ${
                        activeTab === platform.id
                          ? 'border-violets-are-blue/30 bg-gradient-to-tr from-violets-are-blue to-han-purple text-white'
                          : 'border-violets-are-blue/30 bg-violets-are-blue/20 text-white hover:bg-violets-are-blue/30'
                      }`}
                    >
                      {platform.icon} <span>{platform.name}</span>
                    </button>
                  ))}
              </div>
            </div>
          )}

          {platforms.filter(p => p.selected && p.connected).length > 0 && (
            <>
              <div className="mb-1">
                <div className="flex gap-2 justify-between items-end">
                  <div>
                    <p className="text-gray-400 text-sm">
                      Write your own or generate content for {platforms.find((p) => p.id === activeTab)?.name || ''}
                    </p>
                  </div>
                </div>
              </div>

              <PlatformContentTabs
                platforms={platforms}
                setIsImageModalOpen={setIsImageModalOpen}
                activeTab={activeTab}
                onContentChange={handleContentChange}
                setActiveMediaPlatformId={setActiveMediaPlatformId}
                handleRemoveImage={handleRemoveImage}
              />
            </>
          )}

          <SchedulePostActions
            platforms={platforms}
            isScheduling={isScheduling}
            onCancel={handleCancel}
            onSchedule={handleSchedulePost}
            isEditMode={isEditMode}
            hasCharacterLimitViolation={hasCharacterLimitViolation || isLoadingPost}
          />
        </div>
      </DashboardLayout>
    </>
  );
}
